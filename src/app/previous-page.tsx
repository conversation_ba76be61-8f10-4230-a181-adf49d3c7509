"use client";
import OnboardingTour from "@/components/OnboardingTour";
import { Toast } from "@/components/Toast";
import { CardContainer } from "@/components/glass-ui/CardContainer";
import { PrimaryButton } from "@/components/glass-ui/buttons/PrimaryButton";
import { SecondaryButton } from "@/components/glass-ui/buttons/SecondaryButton";
import { GraphsRow } from "@/components/graphs";
import FigmaTableDemo from "@/components/table";
import { Dashboard } from "@/components/dashboard";
import QueryInput from "@/components/query/QueryInput";
import { FileSystem } from "@/components/file-system";
import { BackgroundBeamsWithCollision } from "@/components/ui/background-beams-with-collision";
import { Button } from "@/components/ui/button";
import { Cover } from "@/components/ui/cover";
import { FlipWordsDemo } from "@/components/ui/flip-words";
import { StatefulButton } from "@/components/ui/stateful-button";
import {
  OpeningAnimation,
  PageTransition,
  FadeInSection,
} from "@/components/ui/opening-animation";
import {
  EnhancedBackground,
  FloatingElements,
} from "@/components/ui/enhanced-background";
import PlaneIcon from "@/icons/sidebar/PlaneIcon";
import QueryHistoryIcon from "@/icons/sidebar/QueryHistoryIcon";
import SadIcon from "@/icons/sidebar/SadIcon";
import SendUpIcon from "@/icons/sidebar/SendUpIcon";
import DarkModeToggleIcon from "@/icons/sidebar/darkModeToggleIcon";
import ReloadIcon from "@/icons/sidebar/reloadIcon";
import SearchIcon from "@/icons/sidebar/searchIcon";
import UserIcon from "@/icons/sidebar/userIcon";
import WifiIcon from "@/icons/sidebar/wifiIcon";
import Lottie from "lottie-react";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { FaDownload, FaPlus } from "react-icons/fa";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { useTheme } from "next-themes";
import {
  collectionData,
  collections,
  sampleChartData,
} from "./dummy-data/information";
import { tourApiResponse, tourQueryHistory } from "./dummy-data/tour-data";

import robotAnimation2 from "../../public/robot-lottie3.json";

const quickActions = [
  {
    icon: <WifiIcon />,
    text: "Show me attendance for January 2024",
  },
  {
    icon: <PlaneIcon />,
    text: "Show me the salary list for January 2024",
  },
  {
    icon: <SendUpIcon />,
    text: "Show me the pending task for manager",
  },
  {
    icon: <SadIcon />,
    text: "Show me the profit items",
  },
];

export default function Home() {
  const [query, setQuery] = useState("");
  const [selected, setSelected] = useState<string>("dashboard");
  const [dbResponse, setDbResponse] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<{ [key: string]: string }>({});
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [userId, setUserId] = useState<string>("default");
  const [editingUserId, setEditingUserId] = useState(false);
  const [showUserTooltip, setShowUserTooltip] = useState(false);
  const [history, setHistory] = useState<any[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [historyError, setHistoryError] = useState<string | null>(null);
  const { theme, setTheme } = useTheme();
  const [showCharts, setShowCharts] = useState(true);
  const [showTour, setShowTour] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [isTourMode, setIsTourMode] = useState(false);
  const [reloadLoading, setReloadLoading] = useState(false);
  const [showOpeningAnimation, setShowOpeningAnimation] = useState(true);
  const [toast, setToast] = useState<{
    isVisible: boolean;
    message: string;
    type: "success" | "error" | "info";
  }>({ isVisible: false, message: "", type: "success" });
  const [showBusinessRules, setShowBusinessRules] = useState(false);
  const [businessRulesLoading, setBusinessRulesLoading] = useState(false);
  const [businessRulesError, setBusinessRulesError] = useState<string | null>(
    null
  );
  const [businessRulesText, setBusinessRulesText] = useState<string>("");
  const [downloadingRules, setDownloadingRules] = useState(false);
  const [isEditingRules, setIsEditingRules] = useState(false);
  const [editRulesText, setEditRulesText] = useState("");
  const [editPreview, setEditPreview] = useState(false);
  const [uploadingRules, setUploadingRules] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const editTextareaRef = useRef<HTMLTextAreaElement>(null);
  const userTooltipRef = useRef<HTMLDivElement>(null);
  const [showTourDelayed, setShowTourDelayed] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Fetch query history with "default" user ID on first load
    fetchQueryHistory("default");
    setShowOpeningAnimation(true); // Always show opening animation
  }, []);

  // Handle clicking outside user tooltip
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        userTooltipRef.current &&
        !userTooltipRef.current.contains(event.target as Node)
      ) {
        setShowUserTooltip(false);
        setEditingUserId(false);
      }
    };

    if (showUserTooltip) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showUserTooltip]);

  const fetchQueryHistory = async (userIdentifier: string) => {
    setHistoryLoading(true);
    setHistoryError(null);
    try {
      const res = await fetch(
        `https://************:8000/mssql/conversation-history/${userIdentifier}`
      );
      if (!res.ok) throw new Error("Failed to fetch history");
      const data = await res.json();
      setHistory(Array.isArray(data.payload) ? data.payload : []);
    } catch (e: any) {
      setHistoryError(e.message || "Unknown error");
    } finally {
      setHistoryLoading(false);
    }
  };

  useEffect(() => {
    if (!userId) return;
    fetchQueryHistory(userId);
  }, [userId]);

  // Check for tour completion after component mounts
  useEffect(() => {
    if (!mounted) return;

    // Check if user has seen the tour before
    const hasSeenTour = localStorage.getItem("knowledge-base-tour-completed");
    if (!hasSeenTour) {
      setShowTour(true);
      setIsTourMode(true);
      // Show tour data when tour starts
      setDbResponse(tourApiResponse);
      setHistory(tourQueryHistory);
    }
  }, [mounted]);

  // Show tour data when tour is active
  useEffect(() => {
    if (showTour && !isTourMode) {
      setIsTourMode(true);
      setDbResponse(tourApiResponse);
      setHistory(tourQueryHistory);
    }
  }, [showTour]);

  // Fetch business rules when modal opens
  useEffect(() => {
    if (showBusinessRules) {
      setBusinessRulesLoading(true);
      setBusinessRulesError(null);
      setBusinessRulesText("");
      setIsEditingRules(false);
      setEditRulesText("");
      setEditPreview(false);
      setUploadingRules(false);
      setUploadError(null);
      setUploadSuccess(false);
      fetch("https://************:8000/mssql/get_business-rules", {
        headers: { accept: "text/plain" },
      })
        .then((res) => {
          if (!res.ok) throw new Error("Failed to fetch business rules");
          return res.text();
        })
        .then((text) => setBusinessRulesText(text))
        .catch((e) => setBusinessRulesError(e.message || "Unknown error"))
        .finally(() => setBusinessRulesLoading(false));
    }
  }, [showBusinessRules]);

  const handleClearHistory = async () => {
    setHistoryLoading(true);
    setHistoryError(null);
    try {
      const res = await fetch(
        `https://************:8000/mssql/clear-history/${userId}`,
        { method: "POST", headers: { accept: "application/json" } }
      );
      if (!res.ok) throw new Error("Failed to clear history");
      setHistory([]);
    } catch (e: any) {
      setHistoryError(e.message || "Unknown error");
    } finally {
      setHistoryLoading(false);
    }
  };

  const handleReloadDb = async () => {
    setReloadLoading(true);
    setLoading(true); // Also set main loading state to show animation
    setToast({ isVisible: false, message: "", type: "success" });

    try {
      // Clear current state
      setHistory([]);
      setHistoryLoading(false);
      setHistoryError(null);
      setDbResponse(null);
      setFilters({});
      setSearchTerm("");
      setShowFilters(false);

      const response = await fetch(
        "https://************:8000/mssql/reload-db",
        {
          method: "POST",
          headers: { accept: "application/json" },
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to reload database: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      console.log(data);

      // Show success toast
      setToast({
        isVisible: true,
        message: "Database reloaded successfully!",
        type: "success",
      });
    } catch (error: any) {
      console.error("Reload DB error:", error);
      setToast({
        isVisible: true,
        message:
          error.message || "Failed to reload database. Please try again.",
        type: "error",
      });
    } finally {
      setReloadLoading(false);
      setLoading(false); // Clear main loading state
    }
  };

  const handleQuerySubmit = async () => {
    if (!query.trim()) return;
    if (selected !== "db") return;
    setLoading(true);
    setError(null);
    setDbResponse(null);
    setFilters({});
    setSearchTerm("");
    setShowFilters(false);
    try {
      const res = await fetch(
        "https://************:8000/mssql/query?question=" +
          encodeURIComponent(query) +
          "&user_id=" +
          userId,
        {
          method: "POST",
          headers: { accept: "application/json" },
        }
      );
      if (!res.ok) throw new Error("API error");
      const data = await res.json();
      setDbResponse(data);
      // Re-fetch history after successful query if userId is set and not 'default'
      if (userId && userId !== "default") {
        setHistoryLoading(true);
        setHistoryError(null);
        fetch(`https://************:8000/mssql/conversation-history/${userId}`)
          .then((res) => {
            if (!res.ok) throw new Error("Failed to fetch history");
            return res.json();
          })
          .then((data) => {
            setHistory(Array.isArray(data.payload) ? data.payload : []);
          })
          .catch((e) => setHistoryError(e.message || "Unknown error"))
          .finally(() => setHistoryLoading(false));
      }
    } catch (e: any) {
      setError(e.message || "Unknown error");
    } finally {
      setLoading(false);
    }
    setQuery("");
  };

  const handleTestCharts = () => {
    setLoading(true);
    setError(null);
    setDbResponse(null);
    setFilters({});
    setSearchTerm("");
    setShowFilters(false);

    // Simulate API response with sample data
    setTimeout(() => {
      setDbResponse({
        payload: {
          data: sampleChartData,
        },
      });
      setLoading(false);
    }, 2000); // Increased to 2 seconds to better see the animation
  };

  const data = collectionData[selected];

  // Helper to render dynamic data
  function renderDbData(dataArr: any) {
    if (!Array.isArray(dataArr)) return null;
    if (dataArr.length === 0)
      return <div className="text-gray-400">No data found.</div>;
    if (dataArr.length === 1) {
      const obj = dataArr[0];
      return (
        <div className="card-gradient rounded-2xl p-6 mt-4 max-w-2xl">
          <div className="flex items-center gap-3 mb-4 pb-4 border-b border-[#e1f4ea]/30 dark:border-[#013828]/30">
            <div className="w-12 h-12 flex items-center justify-center rounded-xl bg-[#f0f9f5] dark:bg-[#012920] text-[#00bf6f]">
              <svg
                xmlns="https://www.w3.org/2000/svg"
                className="w-6 h-6"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100">
                Single Record Found
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Detailed information below
              </p>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(obj).map(([key, value]) => (
              <div
                key={key}
                className="flex flex-col gap-1 p-3 rounded-xl bg-[#f0f9f5]/30 dark:bg-[#012920]/30 border border-[#e1f4ea]/30 dark:border-[#013828]/30"
              >
                <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {key}
                </span>
                <span className="text-base text-gray-800 dark:text-gray-100">
                  {String(value)}
                </span>
              </div>
            ))}
          </div>
        </div>
      );
    }
    // Multiple objects: show as list/table with pagination
    const allKeys = Array.from(
      new Set(dataArr.flatMap((obj: any) => Object.keys(obj)))
    );

    // Apply filters
    let filteredData = dataArr;

    // Apply search term
    if (searchTerm) {
      filteredData = filteredData.filter((obj) =>
        Object.values(obj).some((value) =>
          String(value).toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Apply column filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        filteredData = filteredData.filter((obj) =>
          String(obj[key]).toLowerCase().includes(value.toLowerCase())
        );
      }
    });

    // Use filtered data directly - pagination is handled by the table component
    const currentData = filteredData;

    // --- Graph logic ---
    // Enhanced heuristic: Better data type detection and chart selection
    const analyzeDataForCharts = (data: any[], keys: string[]) => {
      const chartData: {
        pie?: { key: string; data: any[] };
        bar?: { key: string; data: any[] };
        line?: { key: string; data: any[] };
      } = {};

      for (const key of keys) {
        const values = data
          .map((obj: any) => obj[key])
          .filter((v) => v !== null && v !== undefined);
        if (values.length === 0) continue;

        const unique = Array.from(new Set(values));
        const isNumeric = values.every(
          (v) => !isNaN(Number(v)) && typeof v !== "boolean"
        );
        const isDate = values.every((v) => !isNaN(Date.parse(v)));
        const uniqueCount = unique.length;

        // Pie chart: categorical data with few unique values (2-8)
        if (
          !chartData.pie &&
          uniqueCount >= 2 &&
          uniqueCount <= 8 &&
          !isNumeric
        ) {
          const counts: Record<string, number> = {};
          data.forEach((obj: any) => {
            const val = String(obj[key] || "Unknown");
            counts[val] = (counts[val] || 0) + 1;
          });
          chartData.pie = {
            key,
            data: Object.entries(counts)
              .sort(([, a], [, b]) => Number(b) - Number(a))
              .map(([name, value]) => ({ name, value })),
          };
        }

        // Bar chart: numeric data or categorical with many values
        if (!chartData.bar && isNumeric && uniqueCount > 1) {
          // Group by X-axis (first key) and sum the metric (key)
          const groupKey = keys[0];
          const grouped = new Map<string, number>();
          data.forEach((obj: any) => {
            const groupVal = obj[groupKey];
            const metricVal = Number(obj[key]) || 0;
            grouped.set(groupVal, (grouped.get(groupVal) || 0) + metricVal);
          });
          const sortedData = Array.from(grouped.entries())
            .map(([name, value]) => ({ name, [key]: value }))
            .sort((a, b) => Number(b[key]) - Number(a[key]))
            .slice(0, 10);
          if (sortedData.length > 0) {
            chartData.bar = { key, data: sortedData };
          }
        }

        // Line chart: time series data or sequential numeric data
        if (!chartData.line && (isDate || (isNumeric && uniqueCount > 5))) {
          let lineData;
          if (isDate) {
            // Group by date and count
            const dateCounts: Record<string, number> = {};
            data.forEach((obj: any) => {
              const date = new Date(obj[key]).toLocaleDateString();
              dateCounts[date] = (dateCounts[date] || 0) + 1;
            });
            lineData = Object.entries(dateCounts)
              .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
              .map(([name, value]) => ({ name, value }));
          } else {
            // Use first 20 items for line chart
            lineData = data.slice(0, 20).map((obj: any, idx) => ({
              name: obj[keys[0]] || `Item ${idx + 1}`,
              value: Number(obj[key]),
            }));
          }

          if (lineData.length > 1) {
            chartData.line = { key, data: lineData };
          }
        }
      }

      return chartData;
    };

    const chartData = analyzeDataForCharts(filteredData, allKeys);

    return (
      <div className="mt-4">
        <FigmaTableDemo
          columns={allKeys.map((key) => ({
            key,
            label: key,
          }))}
          allKeys={allKeys}
          data={currentData}
          pageSizeOptions={[7, 10, 20, 30]}
          defaultPageSize={7}
        />

        {/* Graphs below the table */}
        {(chartData.pie || chartData.bar || chartData.line) && (
          <div className="mt-8 charts-section">
            {/* Chart Toggle */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-700 dark:text-gray-200">
                Data Visualization
              </h3>
              <Button
                variant="outline"
                onClick={() => setShowCharts(!showCharts)}
                className="bg-white/50 dark:bg-[#011f17]/50 border-[#e1f4ea] dark:border-[#013828] text-gray-700 dark:text-gray-200 hover:bg-[#f0f9f5] dark:hover:bg-[#012920] transition-all"
              >
                {showCharts ? "Hide Charts" : "Show Charts"}
              </Button>
            </div>

            <GraphsRow
              chartData={chartData}
              loading={loading}
              isDummy={
                !chartData ||
                (!chartData.line && !chartData.bar && !chartData.pie)
              }
            />

            {/* Charts Container */}
            {/* <div
              className={`transition-all duration-300 overflow-hidden ${
                showCharts ? "max-h-[800px] opacity-100" : "max-h-0 opacity-0"
              }`}
            >
              <div className="flex flex-col lg:flex-row gap-6">
            
                {chartData.pie ? (
                  <Graph
                    type="pie"
                    data={chartData.pie.data}
                    dataKey="value"
                    nameKey="name"
                    title={`Distribution by ${chartData.pie.key}`}
                  />
                ) : (
                  <div className="bg-white/20 opacity-50 backdrop-blur-lg border border-white/30 shadow-lg rounded-2xl p-6 flex-1 min-w-[320px] max-w-[500px] flex flex-col items-center justify-center">
                    <div className="text-gray-500 text-sm text-center">
                      No data to show in this chart
                    </div>
                  </div>
                )}
               
                {chartData.bar ? (
                  <Graph
                    type="bar"
                    data={chartData.bar.data}
                    dataKey={chartData.bar.key}
                    nameKey="name"
                    title={`Top 10 by ${chartData.bar.key}`}
                  />
                ) : (
                  <div className="bg-white/20 opacity-50 backdrop-blur-lg border border-white/30 shadow-lg rounded-2xl p-6 flex-1 min-w-[320px] max-w-[500px] flex flex-col items-center justify-center">
                    <div className="text-gray-500 text-sm text-center">
                      No data to show in this chart
                    </div>
                  </div>
                )}
               
                {chartData.line ? (
                  <Graph
                    type="line"
                    data={chartData.line.data}
                    dataKey="value"
                    nameKey="name"
                    title={`Time Series by ${chartData.line.key}`}
                  />
                ) : (
                  <div className="bg-white/20 opacity-50 backdrop-blur-lg border border-white/30 shadow-lg rounded-2xl p-6 flex-1 min-w-[320px] max-w-[500px] flex flex-col items-center justify-center">
                    <div className="text-gray-500 text-sm text-center">
                      No data to show in this chart
                    </div>
                  </div>
                )}
              </div>
            </div> */}
          </div>
        )}
      </div>
    );
  }

  const handleSaveUserId = (val: string) => {
    setUserId(val || "default");
    setEditingUserId(false);
    setShowUserTooltip(false);
  };

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  // Ensure theme is properly typed for the logo
  const currentTheme = theme as "light" | "dark" | undefined;

  // Fallback theme for logo display
  const logoTheme = currentTheme || "dark";

  const handleTourComplete = () => {
    setShowTour(false);
    setIsTourMode(false);
    // Clear tour data when tour is completed
    if (userId === "default") {
      setDbResponse(null);
      setHistory([]);
    }
    localStorage.setItem("knowledge-base-tour-completed", "true");
  };

  const startTour = () => {
    setShowTour(true);
    setIsTourMode(true);
    setDbResponse(tourApiResponse);
    setHistory(tourQueryHistory);
  };

  // Download business rules file
  const handleDownloadBusinessRules = async () => {
    setDownloadingRules(true);
    try {
      const res = await fetch(
        "https://************:8000/mssql/get_business-rules_file"
      );
      if (!res.ok) throw new Error("Failed to download file");
      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "business_rules.md";
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (e) {
      alert("Failed to download business rules file.");
    } finally {
      setDownloadingRules(false);
    }
  };

  // Start editing
  const handleEditRules = () => {
    setEditRulesText(businessRulesText);
    setIsEditingRules(true);
    setEditPreview(false);
    setUploadError(null);
    setUploadSuccess(false);
    setTimeout(() => {
      editTextareaRef.current?.focus();
    }, 100);
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setIsEditingRules(false);
    setEditRulesText("");
    setEditPreview(false);
    setUploadError(null);
    setUploadSuccess(false);
  };

  // Submit edited rules
  const handleSubmitEdit = async () => {
    setUploadingRules(true);
    setUploadError(null);
    setUploadSuccess(false);
    try {
      // Convert text to Blob and FormData
      const blob = new Blob([editRulesText], { type: "text/markdown" });
      const formData = new FormData();
      formData.append("file", blob, "business_rules.md");
      const res = await fetch(
        "https://************:8000/mssql/update_business-rules",
        {
          method: "PUT",
          body: formData,
        }
      );
      if (!res.ok) throw new Error("Failed to update business rules");
      setUploadSuccess(true);
      // Reload rules after upload
      setBusinessRulesLoading(true);
      fetch("https://************:8000/mssql/get_business-rules", {
        headers: { accept: "text/plain" },
      })
        .then((res) => {
          if (!res.ok) throw new Error("Failed to fetch business rules");
          return res.text();
        })
        .then((text) => setBusinessRulesText(text))
        .catch((e) => setBusinessRulesError(e.message || "Unknown error"))
        .finally(() => setBusinessRulesLoading(false));
      setIsEditingRules(false);
      setEditRulesText("");
      setEditPreview(false);
      setTimeout(() => setUploadSuccess(false), 2000);
    } catch (e: any) {
      setUploadError(e.message || "Failed to update business rules");
    } finally {
      setUploadingRules(false);
    }
  };

  const handleOpeningComplete = () => {
    setShowOpeningAnimation(false);
    // Show main UI immediately, but delay the tour
    setTimeout(() => {
      const hasSeenTour = localStorage.getItem("knowledge-base-tour-completed");
      if (!hasSeenTour) {
        setShowTourDelayed(true);
        setShowTour(true);
        setIsTourMode(true);
        setDbResponse(tourApiResponse);
        setHistory(tourQueryHistory);
      }
    }, 3500); // 3.5 seconds delay
  };

  return (
    <div className="min-h-screen flex">
      {/* Opening Animation */}
      {showOpeningAnimation && (
        <OpeningAnimation duration={4000} onComplete={handleOpeningComplete}>
          <div />
        </OpeningAnimation>
      )}

      {/* Show loading state during hydration */}
      {!mounted && (
        <div className="w-full h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-300">Loading...</p>
          </div>
        </div>
      )}

      {/* Main content - only show when mounted */}
      {mounted && (
        <>
          {/* Sidebar */}
          <PageTransition>
            {/* Logo at the top */}
            <div
              style={{
                width: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                margin: "2px 0 24px 0",
                scale: "1.3",
              }}
            >
              <Image
                src={
                  logoTheme === "dark"
                    ? "/logo/ESAP_W.png"
                    : "/logo/ESAP_B_PNG.png"
                }
                alt="ESAP Logo"
                width={180}
                height={60}
                priority
                style={{
                  objectFit: "contain",
                  maxWidth: 180,
                  height: "auto",
                }}
              />
            </div>
            <CardContainer>
              <aside className="w-full">
                {/* Tab List */}
                <div
                  className="sidebar-tab-list"
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "stretch",
                    gap: "20px",
                    width: "100%",
                  }}
                >
                  {collections.map((col) => (
                    <div key={col.key}>
                      {selected === col.key ? (
                        <PrimaryButton
                          icon={
                            (
                              <col.sidebarIcon fill="#5BE49B" />
                            ) as unknown as string
                          }
                          key={col.key}
                          text={col.name}
                          onClick={() => setSelected(col.key)}
                          className="w-full h-12"
                          mode={theme === "dark" ? "dark" : "light"}
                        />
                      ) : (
                        <div
                          onClick={() => setSelected(col.key)}
                          style={{
                            display: "flex",
                            minHeight: "44px",
                            padding: "4px 8px 4px 12px",
                            alignItems: "center",
                            alignSelf: "stretch",
                            borderRadius: "8px",
                            background:
                              theme === "dark"
                                ? "rgba(255,255,255,0.00)"
                                : "rgba(0,0,0,0.00)",
                            cursor: "pointer",
                            fontWeight: 500,
                            fontSize: "15px",
                            color: theme === "dark" ? "#fff" : "#222",
                            transition: "background 0.2s, color 0.2s",
                            gap: "10px",
                          }}
                          onMouseEnter={(e) => {
                            if (theme !== "dark")
                              e.currentTarget.style.background = "#f3f4f6";
                          }}
                          onMouseLeave={(e) => {
                            if (theme !== "dark")
                              e.currentTarget.style.background =
                                "rgba(0,0,0,0.00)";
                          }}
                        >
                          <col.sidebarIcon
                            fill={theme === "dark" ? "#fff" : "#222"}
                          />
                          {col.name}
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Query History Card - only show when not on dashboard and not on file system */}
                {(isTourMode || userId) &&
                selected !== "dashboard" &&
                selected !== "File system" ? (
                  <div
                    className="query-history"
                    style={{
                      display: "flex",
                      padding: "20px 18px 20px 18px",
                      flexDirection: "column",
                      gap: "12px",
                      alignSelf: "stretch",
                      borderRadius: "24px",
                      border:
                        theme === "dark"
                          ? "2px solid rgba(221,255,237,0.18)"
                          : "1.5px solid #e5e7eb",
                      background:
                        theme === "dark"
                          ? "linear-gradient(180deg, rgba(148,255,212,0.22) -4.62%, rgba(148,255,212,0.09) 52.78%, rgba(148,255,212,0.02) 103.39%)"
                          : "#fff",
                      boxShadow:
                        theme === "dark"
                          ? "0 4px 24px 0 rgba(0,0,0,0.07)"
                          : "0 2px 12px 0 rgba(0,0,0,0.06)",
                      backdropFilter:
                        theme === "dark" ? "blur(32px)" : undefined,
                      marginTop: "30px",
                      width: "100%",
                      color: theme === "dark" ? undefined : "#222",
                    }}
                  >
                    {/* Header: icon, text, clear button */}
                    <div className="flex items-center justify-between w-full mb-2">
                      <div className="flex items-center gap-2">
                        <QueryHistoryIcon className="w-5 h-5" fill="#fff" />
                        <span
                          className="text-base font-semibold dark:text-white text-black"
                          style={{ letterSpacing: "-0.5px" }}
                        >
                          History
                        </span>
                      </div>
                      <button
                        onClick={handleClearHistory}
                        disabled={historyLoading}
                        className="relative w-20 inline-flex h-9 overflow-hidden rounded-full p-[1px] focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50"
                      >
                        <span className="absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#E2CBFF_0%,#393BB2_50%,#E2CBFF_100%)]" />
                        <span className="inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-slate-950 px-3 py-1 text-sm font-medium text-white backdrop-blur-3xl">
                          Clear
                        </span>
                      </button>
                    </div>
                    {/* Query List */}
                    <div className="flex flex-col gap-4 w-full">
                      {history.map((item, i) => (
                        <button
                          key={i}
                          className="w-full text-left truncate text-[15px] font-normal rounded-lg px-3 py-2 transition-all"
                          title={item.question}
                          onClick={() => setQuery(item.question)}
                          style={{
                            fontWeight: 400,
                            color: theme === "dark" ? "#E6F7EF" : "#222",
                            fontSize: "15px",
                            letterSpacing: "-0.2px",
                            boxShadow: "none",
                            background: "transparent",
                          }}
                        >
                          {item.question.length > 32
                            ? item.question.slice(0, 29) + "..."
                            : item.question}
                        </button>
                      ))}
                    </div>
                    {!historyLoading &&
                      !historyError &&
                      history.length === 0 && (
                        <div
                          className="text-xs mt-2 text-center py-2"
                          style={{
                            color: theme === "dark" ? "#B2D9C7" : "#888",
                          }}
                        >
                          No history found
                        </div>
                      )}
                  </div>
                ) : null}
                {/* Business Rules and Plus Button Row - only show when not on dashboard and not on file system */}
                {selected !== "dashboard" && selected !== "File system" && (
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      gap: "16px",
                      marginTop: "30px",
                      alignItems: "center",
                      justifyContent: "flex-start",
                    }}
                  >
                    <SecondaryButton
                      text="Business Rules"
                      iconPlacement="right"
                      icon={(<FaDownload />) as unknown as string}
                      onClick={() => setShowBusinessRules(true)}
                      className="w-full h-12"
                      style={{
                        width: "180px",
                        minHeight: "40px",
                      }}
                      mode={theme === "dark" ? "dark" : "light"}
                    />
                    <button
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: "48px",
                        height: "48px",
                        borderRadius: "100%",
                        border: "1px solid #FFF",
                        background:
                          "radial-gradient(72.6% 80.99% at 50% 50%, rgba(0, 0, 0, 0.50) 50.03%, #C8C8C8 100%), rgba(255, 255, 255, 0.10)",
                        boxShadow: "0 0 16px 0 #FFFFFF33",
                        cursor: "pointer",
                        padding: 0,
                        margin: 0,
                        transition: "box-shadow 0.2s",
                      }}
                      onClick={() => {
                        /* TODO: Add plus button action */
                      }}
                    >
                      <FaPlus fill="#fff" />
                    </button>
                  </div>
                )}
              </aside>
            </CardContainer>
          </PageTransition>
          {/* Main content */}
          <PageTransition className="w-full">
            <EnhancedBackground intensity="medium">
              <main className="flex-1 flex flex-col p-5 gap-6 animate-[fadeIn_0.5s_ease-out_forwards] ml-3">
                {/* Top Bar - Figma style */}
                <FadeInSection delay={0.2}>
                  <div className="flex items-center flex-row justify-between mb-7 w-full">
                    <h1 className="text-2xl font-bold flex items-center gap-2 text-gray-800 dark:text-gray-100"></h1>
                    <div
                      style={{
                        display: "flex",
                        width: "50%",
                        justifyContent: "space-between",
                        alignItems: "center",
                        gap: 12,
                      }}
                    >
                      {/* Search Box */}
                      <div className="flex h-12 px-6 items-center flex-1 rounded-full border-2 bg-white/50 dark:bg-[#232435]/20 border-gray-200 dark:border-white/12 text-gray-800 dark:text-white text-base min-w-0 mr-3 gap-3">
                        <SearchIcon />
                        <input
                          type="text"
                          placeholder="Search"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="bg-transparent border-none outline-none text-gray-800 dark:text-white text-base flex-1 min-w-0"
                        />
                      </div>
                      {/* Reload Button */}
                      <SecondaryButton
                        text="Reload DB"
                        iconPlacement="left"
                        icon={
                          (
                            <ReloadIcon
                              fill={theme === "dark" ? "#fff" : "#222"}
                            />
                          ) as unknown as string
                        }
                        onClick={handleReloadDb}
                        width="180px"
                        style={{
                          minHeight: "40px",
                        }}
                        mode={theme === "dark" ? "dark" : "light"}
                      />
                      {/* Dark Mode Toggle Icon */}
                      <button
                        className="w-12 h-12 rounded-full bg-white/50 dark:bg-[#232435]/50 flex items-center justify-center cursor-pointer transition-colors"
                        onClick={toggleTheme}
                        aria-label="Toggle dark mode"
                      >
                        <DarkModeToggleIcon width={28} height={28} />
                      </button>
                      {/* User Icon with Tooltip */}
                      <div className="relative">
                        <button
                          className="w-12 h-12 rounded-full bg-white/50 dark:bg-[#232435]/50 flex items-center justify-center overflow-hidden cursor-pointer z-10 transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowUserTooltip(!showUserTooltip);
                          }}
                          aria-label="User settings"
                        >
                          <UserIcon width={36} height={36} />
                        </button>

                        {/* User ID Tooltip */}
                        {showUserTooltip && (
                          <div
                            style={{
                              zIndex: 1000,
                            }}
                            ref={userTooltipRef}
                            className="absolute top-15 right-0 w-70 backdrop-blur-12 border-2 border-green-200/27 dark:border-green-200/27 bg-gradient-to-b from-green-200/25 via-green-200/9 to-green-200/2 dark:from-green-200/25 dark:via-green-200/9 dark:to-green-200/2 rounded-2xl p-4 shadow-2xl z-40"
                          >
                            <div className="mb-3">
                              <h3 className="m-0 mb-2 text-sm font-semibold text-gray-800 dark:text-white">
                                User ID Settings
                              </h3>
                              <p className="m-0 text-xs text-gray-600 dark:text-gray-300">
                                {userId === "default"
                                  ? "Set your user ID to save query history"
                                  : `Current User ID: ${userId}`}
                              </p>
                            </div>

                            {editingUserId ? (
                              <div
                                style={{
                                  display: "flex",
                                  flexDirection: "column",
                                  gap: "8px",
                                }}
                              >
                                <input
                                  type="text"
                                  placeholder="Enter user ID"
                                  defaultValue={
                                    userId === "default" ? "" : userId
                                  }
                                  autoFocus
                                  style={{
                                    background: "rgba(255, 255, 255, 0.1)",
                                    border:
                                      "1px solid rgba(255, 255, 255, 0.2)",
                                    borderRadius: "8px",
                                    padding: "8px 12px",
                                    color: "#FFFFFF",
                                    fontSize: "14px",
                                    outline: "none",
                                  }}
                                  onKeyDown={(e) => {
                                    if (e.key === "Enter") {
                                      e.preventDefault();
                                      handleSaveUserId(
                                        (e.target as HTMLInputElement).value
                                      );
                                    }
                                  }}
                                />
                                <div
                                  style={{
                                    display: "flex",
                                    gap: "8px",
                                    justifyContent: "flex-end",
                                  }}
                                >
                                  <button
                                    onClick={() => {
                                      setEditingUserId(false);
                                      setShowUserTooltip(false);
                                    }}
                                    style={{
                                      background: "rgba(255, 255, 255, 0.1)",
                                      border:
                                        "1px solid rgba(255, 255, 255, 0.2)",
                                      borderRadius: "6px",
                                      padding: "6px 12px",
                                      color: "#FFFFFF",
                                      fontSize: "12px",
                                      cursor: "pointer",
                                    }}
                                  >
                                    Cancel
                                  </button>
                                  <button
                                    onClick={() => {
                                      const input = document.querySelector(
                                        'input[placeholder="Enter user ID"]'
                                      ) as HTMLInputElement;
                                      handleSaveUserId(input?.value || "");
                                    }}
                                    style={{
                                      background: "#5BE49B",
                                      border: "none",
                                      borderRadius: "6px",
                                      padding: "6px 12px",
                                      color: "#000000",
                                      fontSize: "12px",
                                      fontWeight: 500,
                                      cursor: "pointer",
                                    }}
                                  >
                                    Save
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <button
                                onClick={() => setEditingUserId(true)}
                                style={{
                                  background: "rgba(255, 255, 255, 0.1)",
                                  border: "1px solid rgba(255, 255, 255, 0.2)",
                                  borderRadius: "8px",
                                  padding: "8px 12px",
                                  color: "#FFFFFF",
                                  fontSize: "14px",
                                  cursor: "pointer",
                                  width: "100%",
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  gap: "8px",
                                }}
                              >
                                <span>✏️</span>
                                {userId === "default"
                                  ? "Set User ID"
                                  : "Change User ID"}
                              </button>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  {/* Query input section - only show when not on dashboard and not on file system */}
                  {selected !== "dashboard" && selected !== "File system" && (
                    <QueryInput
                      query={query}
                      setQuery={setQuery}
                      handleQuerySubmit={handleQuerySubmit}
                      loading={loading}
                      selected={selected}
                      quickActions={quickActions}
                      theme={theme}
                    />
                  )}
                </FadeInSection>
                {/* Show Dashboard */}
                {selected === "dashboard" && (
                  <Dashboard
                    data={data}
                    onNavigate={(key) => setSelected(key)}
                  />
                )}

                {/* Show File System */}
                {selected === "File system" && <FileSystem theme={theme} />}

                {/* Show response for DB Knowledge only */}
                {selected === "db" && (
                  <div>
                    {error && <div className="text-red-500 mt-2">{error}</div>}

                    {/* Show Lottie animation when loading and not in tour mode */}
                    {loading && !isTourMode && (
                      <div className="flex flex-col items-center justify-center h-full min-h-[400px] gap-6">
                        <div className="w-64 h-64">
                          <Lottie
                            animationData={robotAnimation2}
                            loop={true}
                            autoplay={true}
                          />
                        </div>
                        <div className="text-center">
                          <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-2">
                            Searching for answers...
                          </h3>
                          <p className="text-gray-500 dark:text-gray-400">
                            Our AI is analyzing your query and searching through
                            the database
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Show data when not loading and data is available */}
                    {!loading &&
                      dbResponse &&
                      dbResponse.payload &&
                      renderDbData(dbResponse.payload.data)}

                    {/* Show BackgroundLinesDemo when no data is available and not loading */}
                    {!loading &&
                      !error &&
                      (!dbResponse ||
                        !dbResponse.payload ||
                        !dbResponse.payload.data ||
                        (Array.isArray(dbResponse.payload.data) &&
                          dbResponse.payload.data.length === 0)) && (
                        <div className="flex items-center justify-center h-full min-h-[200px]">
                          <h1 className="text-4xl md:text-4xl lg:text-6xl font-semibold max-w-7xl mx-auto text-center mt-6 relative z-20 py-6 bg-clip-text text-transparent bg-gradient-to-b from-neutral-800 via-neutral-700 to-neutral-700 dark:from-neutral-800 dark:via-white dark:to-white">
                            Query your database <br /> at{" "}
                            <Cover className="text-4xl md:text-4xl lg:text-6xl">
                              warp speed
                            </Cover>
                          </h1>
                        </div>
                      )}
                  </div>
                )}
              </main>
            </EnhancedBackground>

            {/* Onboarding Tour */}
            {mounted &&
              !showOpeningAnimation &&
              showTourDelayed &&
              showTour && (
                <OnboardingTour
                  isVisible={showTour}
                  onComplete={handleTourComplete}
                  theme={logoTheme}
                />
              )}

            {/* Business Rules Modal */}
            {showBusinessRules && (
              <div
                className="fixed inset-0 z-50 bg-black/40"
                style={{
                  position: "fixed",
                  inset: 0,
                  zIndex: 50,
                  background: "rgba(0,0,0,0.40)",
                }}
              >
                <div
                  className="w-863 p-2 px-6 pb-6 flex flex-col justify-center items-center flex-shrink-0 rounded-3xl border-2"
                  style={{
                    position: "fixed",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    borderRadius: "32px",
                    border:
                      theme === "dark"
                        ? "3px solid rgba(0, 191, 111, 0.27)"
                        : "1.5px solid #e1f4ea",
                    background:
                      theme === "dark"
                        ? "linear-gradient(180deg, rgba(0, 191, 111, 0.25) 0%, rgba(0, 191, 111, 0.09) 52.11%, rgba(0, 191, 111, 0.02) 100%)"
                        : "#fff",
                    boxShadow:
                      theme === "dark"
                        ? "none"
                        : "0 2px 12px 0 rgba(0,0,0,0.06)",
                    backdropFilter: theme === "dark" ? "blur(32px)" : undefined,
                    maxWidth: "95vw",
                    maxHeight: "95vh",
                    overflow: "auto",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  {/* Header */}
                  <div className="flex items-center w-full  p-0 mb-3">
                    <h2 className="text-xl font-bold text-gray-800 dark:text-gray-100 flex-1">
                      Business Rules
                    </h2>
                    <button
                      className="text-gray-500 hover:text-gray-800 dark:hover:text-gray-200 text-2xl px-2 py-1 rounded-full focus:outline-none"
                      onClick={() => setShowBusinessRules(false)}
                      aria-label="Close"
                    >
                      ×
                    </button>
                  </div>
                  {/* Inner Card for Content */}
                  <div
                    className="flex p-6 items-start gap-6 self-stretch rounded-3xl w-full mt-6 min-h-300"
                    style={{
                      background:
                        theme === "dark"
                          ? "rgba(0, 191, 111, 0.10)"
                          : "#f0f9f5",
                      borderRadius: "24px",
                    }}
                  >
                    {/* Content */}
                    <div className="w-full">
                      {businessRulesLoading ? (
                        <div className="flex items-center justify-center h-full">
                          <svg
                            className="animate-spin h-8 w-8 text-[#00bf6f]"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                        </div>
                      ) : businessRulesError ? (
                        <div className="text-red-500 text-center font-medium">
                          {businessRulesError}
                        </div>
                      ) : isEditingRules ? (
                        editPreview ? (
                          <div className="flex flex-col h-full w-full">
                            <div className="flex-1 markdown-body max-h-[55vh] overflow-auto bg-gray-50 dark:bg-[#012920]/40 rounded-xl p-4 border border-gray-100 dark:border-[#013828]/40">
                              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                                {editRulesText}
                              </ReactMarkdown>
                            </div>
                            <div className="flex gap-2 mt-4 justify-end max-w-40">
                              <PrimaryButton
                                onClick={() => setEditPreview(false)}
                                disabled={uploadingRules}
                                text="Back to Edit"
                              />
                            </div>
                          </div>
                        ) : (
                          <div className="flex flex-col h-full w-full">
                            <div className="flex-1 min-h-0 flex flex-col">
                              <textarea
                                ref={editTextareaRef}
                                className="w-full flex-1 rounded-xl border border-gray-200 dark:border-[#013828]/40 p-4 font-mono text-sm text-gray-800 dark:text-gray-100 bg-gray-50 dark:bg-[#012920]/40 resize-none focus:outline-none"
                                style={{
                                  minHeight: "300px",
                                  maxHeight: "55vh",
                                  height: "100%",
                                }}
                                value={editRulesText}
                                onChange={(e) =>
                                  setEditRulesText(e.target.value)
                                }
                                disabled={uploadingRules}
                              />
                            </div>
                            <div className="flex gap-2 mt-4">
                              <div className="">
                                <PrimaryButton
                                  text="Submit"
                                  onClick={handleSubmitEdit}
                                  disabled={uploadingRules}
                                  className="h-[40px] w-32"
                                />
                              </div>
                              <SecondaryButton
                                text="Cancel"
                                style={{ width: "70px" }}
                                onClick={handleCancelEdit}
                                disabled={uploadingRules}
                              />
                              <SecondaryButton
                                text="Preview"
                                style={{ width: "70px" }}
                                onClick={() => setEditPreview(true)}
                                disabled={uploadingRules}
                              />
                            </div>
                            {uploadError && (
                              <div className="text-red-500 mt-2">
                                {uploadError}
                              </div>
                            )}
                            {uploadSuccess && (
                              <div className="text-green-600 mt-2">
                                Business rules updated!
                              </div>
                            )}
                          </div>
                        )
                      ) : (
                        <div className="markdown-body max-h-[55vh] overflow-auto text-gray-800 dark:text-white text-base leading-7">
                          <ReactMarkdown remarkPlugins={[remarkGfm]}>
                            {businessRulesText}
                          </ReactMarkdown>
                        </div>
                      )}
                    </div>
                  </div>
                  {/* Footer */}
                  {!isEditingRules && (
                    <div className="flex items-center justify-between w-full mt-6">
                      <SecondaryButton
                        style={{}}
                        className="h-8"
                        text="Cancel"
                        onClick={() => setShowBusinessRules(false)}
                        mode={theme === "dark" ? "dark" : "light"}
                      />
                      <div className="flex items-center gap-2">
                        {!businessRulesLoading && !isEditingRules && (
                          <SecondaryButton
                            text="Edit"
                            onClick={handleEditRules}
                            style={{ width: "70px" }}
                            mode={theme === "dark" ? "dark" : "light"}
                          />
                        )}
                        <PrimaryButton
                          text="Download"
                          onClick={handleDownloadBusinessRules}
                          disabled={businessRulesLoading || downloadingRules}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Toast Component */}
            <Toast
              isVisible={toast.isVisible}
              message={toast.message}
              type={toast.type}
              onClose={() =>
                setToast({ isVisible: false, message: "", type: "success" })
              }
            />
          </PageTransition>
        </>
      )}
    </div>
  );
}

/*
.markdown-body h1 { font-size: 1.5rem; font-weight: bold; margin-top: 1.2em; margin-bottom: 0.5em; }
.markdown-body h2 { font-size: 1.25rem; font-weight: bold; margin-top: 1em; margin-bottom: 0.5em; }
.markdown-body h3 { font-size: 1.1rem; font-weight: bold; margin-top: 0.8em; margin-bottom: 0.4em; }
.markdown-body ul, .markdown-body ol { margin-left: 1.5em; margin-bottom: 1em; }
.markdown-body li { margin-bottom: 0.3em; }
.markdown-body code, .markdown-body pre { background: #f3f3f3; color: #333; border-radius: 4px; padding: 2px 6px; }
*/