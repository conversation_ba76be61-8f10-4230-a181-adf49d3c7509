"use client";

import React from "react";

interface DashboardProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardProps) {
  return (
    <div className="relative min-h-screen overflow-hidden">
      {/* Decorative Background Blobs */}
      <div className="fixed inset-0 pointer-events-none">
        {/* Top Right Blob */}
        <div
          className="absolute"
          style={{
            width: "min(753px, 40vw)",
            height: "min(572px, 30vh)",
            right: "-20vw",
            top: "-15vh",
            backgroundColor: "#614A64",
            opacity: 0.3,
            borderRadius: "50%",
            filter: "blur(97px)",
            zIndex: -30,
          }}
        />

        {/* Top Center Blob */}
        <div
          className="absolute"
          style={{
            width: "min(753px, 40vw)",
            height: "min(533px, 28vh)",
            left: "50%",
            top: "-25vh",
            transform: "translateX(-50%)",
            backgroundColor: "#94FFD4",
            opacity: 0.3,
            borderRadius: "50%",
            filter: "blur(97px)",
            zIndex: -30,
          }}
        />

        {/* Bottom Center Blob */}
        <div
          className="absolute"
          style={{
            width: "min(753px, 40vw)",
            height: "min(533px, 28vh)",
            left: "50%",
            bottom: "-15vh",
            transform: "translateX(-50%)",
            backgroundColor: "rgba(255,135,253,0.8)",
            opacity: 0.3,
            borderRadius: "50%",
            filter: "blur(150px)",
            zIndex: -30,
          }}
        />

        {/* Left Side Blob */}
        <div
          className="absolute"
          style={{
            width: "min(600px, 35vw)",
            height: "min(600px, 35vh)",
            left: "-20vw",
            top: "50%",
            transform: "translateY(-50%)",
            backgroundColor: "rgba(94, 228, 155, 0.6)",
            opacity: 0.2,
            borderRadius: "50%",
            filter: "blur(120px)",
            zIndex: -30,
          }}
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
}
