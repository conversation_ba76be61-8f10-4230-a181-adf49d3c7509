"use client";
import { useEffect, useRef, useState } from "react";
import { useTheme } from "next-themes";
import <PERSON><PERSON> from "lottie-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

// Components
import OnboardingTour from "@/components/OnboardingTour";
import { Dashboard } from "@/components/dashboard";
import QueryInput from "@/components/query/QueryInput";
import { FileSystem } from "@/components/file-system";
import { Cover } from "@/components/ui/cover";
import { FlipWordsDemo } from "@/components/ui/flip-words";
import { FadeInSection } from "@/components/ui/opening-animation";
import { MainLayout } from "@/components/layout/MainLayout";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { TopBar } from "@/components/layout/TopBar";
import { DataRenderer } from "@/components/data/DataRenderer";
import { DashboardLayout } from "./DashboardLayout";

// Icons
import PlaneIcon from "@/icons/sidebar/PlaneIcon";
import SadIcon from "@/icons/sidebar/SadIcon";
import SendUpIcon from "@/icons/sidebar/SendUpIcon";
import WifiIcon from "@/icons/sidebar/wifiIcon";

// Hooks
import { useDatabaseOperations } from "@/lib/hooks";

// Data
import {
  collectionData,
  collections,
  sampleChartData,
} from "./dummy-data/information";
import { tourApiResponse } from "./dummy-data/tour-data";

// Assets
import robotAnimation2 from "../../public/robot-lottie3.json";

const quickActions = [
  {
    icon: <WifiIcon />,
    text: "Show me attendance for January 2024",
  },
  {
    icon: <PlaneIcon />,
    text: "Show me the salary list for January 2024",
  },
  {
    icon: <SendUpIcon />,
    text: "Show me the pending task for manager",
  },
  {
    icon: <SadIcon />,
    text: "Show me the profit items",
  },
];

export default function Home() {
  // Theme
  const { theme, setTheme } = useTheme();
  
  // Database operations hook
  const {
    loading,
    error,
    dbResponse,
    toast,
    fetchQueryHistory,
    sendDatabaseQuery,
    setDbResponse,
  } = useDatabaseOperations();

  // Local state
  const [query, setQuery] = useState("");
  const [selected, setSelected] = useState<string>("dashboard");
  const [filters, setFilters] = useState<{ [key: string]: string }>({});
  const [searchTerm, setSearchTerm] = useState("");
  const [userId, setUserId] = useState<string>("default");
  const [editingUserId, setEditingUserId] = useState(false);
  const [showUserTooltip, setShowUserTooltip] = useState(false);
  const [showCharts, setShowCharts] = useState(true);
  const [showTour, setShowTour] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [isTourMode, setIsTourMode] = useState(false);
  const [showOpeningAnimation, setShowOpeningAnimation] = useState(true);
  
  // Refs
  const userTooltipRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setMounted(true);
    // Fetch query history with "default" user ID on first load
    fetchQueryHistory("default");
    setShowOpeningAnimation(true); // Always show opening animation
  }, []);

  // Handle clicking outside user tooltip
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        userTooltipRef.current &&
        !userTooltipRef.current.contains(event.target as Node)
      ) {
        setShowUserTooltip(false);
        setEditingUserId(false);
      }
    };

    if (showUserTooltip) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showUserTooltip]);

  useEffect(() => {
    if (!userId) return;
    fetchQueryHistory(userId);
  }, [userId]);

  // Check for tour completion after component mounts
  useEffect(() => {
    if (!mounted) return;

    // Check if user has seen the tour before
    const hasSeenTour = localStorage.getItem("knowledge-base-tour-completed");
    if (!hasSeenTour) {
      setShowTour(true);
      setIsTourMode(true);
      // Show tour data when tour starts
      setDbResponse(tourApiResponse);
    }
  }, [mounted]);

  // Show tour data when tour is active
  useEffect(() => {
    if (showTour && !isTourMode) {
      setIsTourMode(true);
      setDbResponse(tourApiResponse);
    }
  }, [showTour]);

  const handleQuerySubmit = async () => {
    if (!query.trim() || selected !== "db") return;
    
    // Reset UI state
    setFilters({});
    setSearchTerm("");
    
    // Send query
    await sendDatabaseQuery(query, userId);
    setQuery("");
  };

  // Function to load sample chart data for testing
  const handleTestCharts = () => {
    // Reset state
    setDbResponse(null);
    setFilters({});
    setSearchTerm("");
    
    // Simulate API response with sample data
    setTimeout(() => {
      setDbResponse({
        payload: {
          data: sampleChartData,
        },
      });
    }, 2000);
  };

  const handleSaveUserId = (val: string) => {
    setUserId(val || "default");
    setEditingUserId(false);
    setShowUserTooltip(false);
  };

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  const handleTourComplete = () => {
    setShowTour(false);
    setIsTourMode(false);
    // Clear tour data when tour is completed
    if (userId === "default") {
      setDbResponse(null);
    }
    localStorage.setItem("knowledge-base-tour-completed", "true");
  };

  const startTour = () => {
    setShowTour(true);
    setIsTourMode(true);
    setDbResponse(tourApiResponse);
  };

  const handleOpeningComplete = () => {
    setShowOpeningAnimation(false);
    // Show main UI immediately, but delay the tour
    setTimeout(() => {
      const hasSeenTour = localStorage.getItem("knowledge-base-tour-completed");
      if (!hasSeenTour) {
        setShowTour(true);
        setIsTourMode(true);
        setDbResponse(tourApiResponse);
      }
    }, 3500); // 3.5 seconds delay
  };

  const handleQuickActionClick = (text: string) => {
    setQuery(text);
    setSelected("db");
  };

  const handleUserIdClick = () => {
    setShowUserTooltip(!showUserTooltip);
    setEditingUserId(true);
  };

  // Get current data based on selected tab
  const data = collectionData[selected];

  return (
    <MainLayout
      showOpeningAnimation={showOpeningAnimation}
      onOpeningComplete={handleOpeningComplete}
      toast={toast}
      mounted={mounted}
    >
      <DashboardLayout>
        <div className="flex min-h-screen">
          {/* Sidebar */}
          <div className="w-80 flex-shrink-0 p-6 bg-white/5 dark:bg-black/5 backdrop-blur-sm border-r border-white/10 dark:border-gray-700/20">
            <AppSidebar
              selected={selected}
              setSelected={setSelected}
              collections={collections}
              quickActions={quickActions}
              onQuickActionClick={handleQuickActionClick}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
          {/* Top Bar */}
          <TopBar
            userId={userId}
            editingUserId={editingUserId}
            showUserTooltip={showUserTooltip}
            userTooltipRef={userTooltipRef as React.RefObject<HTMLDivElement>}
            onUserIdClick={handleUserIdClick}
            onSaveUserId={handleSaveUserId}
            onToggleTheme={toggleTheme}
            onStartTour={startTour}
          />

        {/* Content Area */}
        <div className="flex-1 p-6 overflow-auto">
          {selected === "dashboard" && (
            <FadeInSection>
              <Dashboard 
                data={{ cards: [] }} 
                onNavigate={setSelected} 
              />
            </FadeInSection>
          )}

          {selected === "db" && (
            <div className="space-y-6">
              <div className="text-center">
                <Cover>
                  <FlipWordsDemo />
                </Cover>
              </div>

              <QueryInput
                query={query}
                setQuery={setQuery}
                handleQuerySubmit={handleQuerySubmit}
                loading={loading}
                selected={selected}
                quickActions={quickActions}
                theme={theme}
              />

              {error && (
                <div className="text-red-500 text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  {error}
                </div>
              )}

              {dbResponse?.payload?.data && (
                <DataRenderer
                  data={dbResponse.payload.data}
                  showCharts={showCharts}
                  loading={loading}
                  onToggleCharts={() => setShowCharts(!showCharts)}
                  filters={filters}
                  searchTerm={searchTerm}
                />
              )}
            </div>
          )}

          {selected === "files" && (
            <FadeInSection>
              <FileSystem theme={theme} />
            </FadeInSection>
          )}

          {/* Other content sections */}
          {selected !== "dashboard" &&
            selected !== "db" &&
            selected !== "files" &&
            data && (
              <FadeInSection>
                <div className="space-y-6">
                  <div className="text-center">
                    <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-4">
                      {collections.find((c) => c.key === selected)?.name}
                    </h2>
                  </div>

                  {data && typeof data === 'object' && 'content' in data && (
                    <div className="prose dark:prose-invert max-w-none">
                      <ReactMarkdown remarkPlugins={[remarkGfm]}>
                        {data.content as string}
                      </ReactMarkdown>
                    </div>
                  )}
                </div>
              </FadeInSection>
            )}
          </div>
        </div>
        </div>
      </DashboardLayout>

      {/* Onboarding Tour */}
      {showTour && (
        <OnboardingTour
          isVisible={showTour}
          onComplete={handleTourComplete}
        />
      )}

      {/* Loading Animation */}
      {loading && (
        <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 flex flex-col items-center gap-4">
            <Lottie
              animationData={robotAnimation2}
              style={{ width: 120, height: 120 }}
              loop={true}
            />
            <p className="text-gray-600 dark:text-gray-300">
              Processing your request...
            </p>
          </div>
        </div>
      )}
    </MainLayout>
  );
}