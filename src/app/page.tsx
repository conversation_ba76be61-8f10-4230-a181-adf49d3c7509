"use client";
import React, { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

// Components
import OnboardingTour from "@/components/OnboardingTour";
import { Dashboard } from "@/components/dashboard";
import QueryInput from "@/components/query/QueryInput";
import { FileSystem } from "@/components/file-system";

import { FadeInSection } from "@/components/ui/opening-animation";
import { LoadingAnimation } from "@/components/ui/LoadingAnimation";
import { MainLayout } from "@/components/layout/MainLayout";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { TopBar } from "@/components/layout/TopBar";
import { QueryHistoryCard } from "@/components/layout/QueryHistoryCard";
import { UserTooltip } from "@/components/layout/UserTooltip";
import { DataRenderer } from "@/components/data/DataRenderer";
import { BusinessRulesModal } from "@/components/modals/BusinessRulesModal";
import { DashboardLayout } from "./DashboardLayout";

// Icons
import PlaneIcon from "@/icons/sidebar/PlaneIcon";
import SadIcon from "@/icons/sidebar/SadIcon";
import SendUpIcon from "@/icons/sidebar/SendUpIcon";
import WifiIcon from "@/icons/sidebar/wifiIcon";

// Hooks
import {
  useDatabaseOperations,
  useBusinessRulesModal,
  useUserSettings,
  useTour
} from "@/lib/hooks";

// Data
import {
  collectionData,
  collections,
} from "./dummy-data/information";

// Assets
import robotAnimation2 from "../../public/robot-lottie3.json";

const quickActions = [
  {
    icon: <WifiIcon />,
    text: "Show me attendance for January 2024",
  },
  {
    icon: <PlaneIcon />,
    text: "Show me the salary list for January 2024",
  },
  {
    icon: <SendUpIcon />,
    text: "Show me the pending task for manager",
  },
  {
    icon: <SadIcon />,
    text: "Show me the profit items",
  },
];

export default function Home() {
  // Theme
  const { theme, setTheme } = useTheme();
  
  // Core state
  const [query, setQuery] = useState("");
  const [selected, setSelected] = useState<string>("dashboard");
  const [filters, setFilters] = useState<{ [key: string]: string }>({});
  const [searchTerm, setSearchTerm] = useState("");
  const [showCharts, setShowCharts] = useState(true);
  const [showOpeningAnimation, setShowOpeningAnimation] = useState(true);

  // Custom hooks
  const {
    loading,
    error,
    dbResponse,
    toast,
    history,
    historyLoading,
    historyError,
    fetchQueryHistory,
    sendDatabaseQuery,
    clearHistory,
  } = useDatabaseOperations();

  const businessRulesModal = useBusinessRulesModal();
  const userSettings = useUserSettings();
  const tour = useTour();

  // Initialize on mount
  useEffect(() => {
    fetchQueryHistory(userSettings.userId);
  }, [fetchQueryHistory, userSettings.userId]);

  // Event handlers
  const handleQuerySubmit = async () => {
    if (!query.trim() || selected !== "db") return;

    // Reset UI state
    setFilters({});
    setSearchTerm("");

    // Send query
    await sendDatabaseQuery(query, userSettings.userId);
    setQuery("");
  };

  const handleQuickActionClick = (text: string) => {
    setQuery(text);
  };

  const toggleTheme = () => {
    setTheme(theme === "light" ? "dark" : "light");
  };

  const handleOpeningComplete = () => {
    setShowOpeningAnimation(false);
    tour.handleOpeningComplete();
  };

  // Get current data based on selected tab
  const data = collectionData[selected];

  // Get tour data if in tour mode
  const tourData = tour.getTourData();
  const displayDbResponse = tour.isTourMode ? tourData.dbResponse : dbResponse;
  const displayHistory = tour.isTourMode ? tourData.history : history;

  return (
    <MainLayout
      showOpeningAnimation={showOpeningAnimation}
      onOpeningComplete={handleOpeningComplete}
      toast={toast}
      mounted={tour.mounted}
    >
      <DashboardLayout>
        <div className="flex h-screen">
          {/* Sidebar */}
          <div className="w-80 flex-shrink-0 p-6 bg-white/5 dark:bg-black/5 backdrop-blur-sm border-r border-white/10 dark:border-gray-700/20">
            <AppSidebar
              selected={selected}
              setSelected={setSelected}
              collections={collections}
              quickActions={quickActions}
              onQuickActionClick={handleQuickActionClick}
            />

            {/* Query History Card */}
            <QueryHistoryCard
              history={displayHistory}
              historyLoading={historyLoading}
              historyError={historyError}
              isTourMode={tour.isTourMode}
              userId={userSettings.userId}
              selected={selected}
              onClearHistory={() => clearHistory(userSettings.userId)}
              onQuerySelect={setQuery}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Top Bar */}
            <TopBar
              userId={userSettings.userId}
              editingUserId={userSettings.editingUserId}
              showUserTooltip={userSettings.showUserTooltip}
              userTooltipRef={userSettings.userTooltipRef}
              onUserIdClick={userSettings.handleUserIdClick}
              onSaveUserId={userSettings.handleSaveUserId}
              onToggleTheme={toggleTheme}
              onStartTour={tour.startTour}
            />

            {/* User Tooltip */}
            <UserTooltip
              userId={userSettings.userId}
              editingUserId={userSettings.editingUserId}
              showUserTooltip={userSettings.showUserTooltip}
              userTooltipRef={userSettings.userTooltipRef}
              onSaveUserId={userSettings.handleSaveUserId}
              onToggleEdit={userSettings.toggleEditUserId}
              onClose={userSettings.closeTooltip}
            />

            {/* Content Area */}
            <div className="flex-1 p-6 overflow-auto">
              {selected === "dashboard" && (
                <FadeInSection>
                  <Dashboard
                    data={{ cards: [] }}
                    onNavigate={setSelected}
                  />
                </FadeInSection>
              )}

              {selected === "db" && (
                <div className="space-y-6">
                  <QueryInput
                    query={query}
                    setQuery={setQuery}
                    handleQuerySubmit={handleQuerySubmit}
                    loading={loading}
                    selected={selected}
                    quickActions={quickActions}
                    theme={theme}
                  />

                  {error && (
                    <div className="text-red-500 text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                      {error}
                    </div>
                  )}

                  {displayDbResponse?.payload?.data && (
                    <DataRenderer
                      data={displayDbResponse.payload.data}
                      showCharts={showCharts}
                      loading={loading}
                      onToggleCharts={() => setShowCharts(!showCharts)}
                      filters={filters}
                      searchTerm={searchTerm}
                    />
                  )}
                </div>
              )}

              {selected === "files" && (
                <FadeInSection>
                  <FileSystem theme={theme} />
                </FadeInSection>
              )}

              {/* Other content sections */}
              {selected !== "dashboard" &&
                selected !== "db" &&
                selected !== "files" &&
                data && (
                  <FadeInSection>
                    <div className="space-y-6">
                      <div className="text-center">
                        <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-4">
                          {collections.find((c) => c.key === selected)?.name}
                        </h2>
                      </div>

                      {data && typeof data === 'object' && 'content' in data && (
                        <div className="prose dark:prose-invert max-w-none">
                          <ReactMarkdown remarkPlugins={[remarkGfm]}>
                            {data.content as string}
                          </ReactMarkdown>
                        </div>
                      )}
                    </div>
                  </FadeInSection>
                )}
            </div>
          </div>
        </div>
      </DashboardLayout>

      {/* Business Rules Modal */}
      <BusinessRulesModal
        isOpen={businessRulesModal.showBusinessRules}
        businessRulesText={businessRulesModal.businessRulesText}
        businessRulesLoading={businessRulesModal.businessRulesLoading}
        businessRulesError={businessRulesModal.businessRulesError}
        isEditingRules={businessRulesModal.isEditingRules}
        editRulesText={businessRulesModal.editRulesText}
        editPreview={businessRulesModal.editPreview}
        uploadingRules={businessRulesModal.uploadingRules}
        uploadError={businessRulesModal.uploadError}
        uploadSuccess={businessRulesModal.uploadSuccess}
        downloadingRules={businessRulesModal.downloadingRules}
        onClose={businessRulesModal.closeModal}
        onEditRulesTextChange={businessRulesModal.setEditRulesText}
        onStartEdit={businessRulesModal.startEdit}
        onCancelEdit={businessRulesModal.cancelEdit}
        onSubmitEdit={businessRulesModal.submitEdit}
        onTogglePreview={businessRulesModal.togglePreview}
        onDownload={businessRulesModal.handleDownload}
      />

      {/* Onboarding Tour */}
      {tour.showTour && (
        <OnboardingTour
          isVisible={tour.showTour}
          onComplete={tour.handleTourComplete}
        />
      )}

      {/* Loading Animation */}
      <LoadingAnimation
        isVisible={loading}
        animationData={robotAnimation2}
        title="Processing your request..."
        subtitle="Our AI is analyzing your query and searching through the database"
      />
    </MainLayout>
  );
}