"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SecondaryButton } from "@/components/glass-ui/buttons/SecondaryButton";
import { useTheme } from "next-themes";
import { useState } from "react";
import SearchIcon from "@/icons/sidebar/searchIcon";
import ReloadIcon from "@/icons/sidebar/reloadIcon";
import DarkModeToggleIcon from "@/icons/sidebar/darkModeToggleIcon";
import UserIcon from "@/icons/sidebar/userIcon";

interface TopBarProps {
  userId?: string;
  editingUserId?: boolean;
  showUserTooltip?: boolean;
  userTooltipRef?: React.RefObject<HTMLDivElement | null>;
  onUserIdClick?: () => void;
  onSaveUserId?: (userId: string) => void;
  onToggleTheme?: () => void;
  onStartTour?: () => void;
  searchTerm?: string;
  onSearchChange?: (value: string) => void;
  onReloadDb?: () => void;
  reloadLoading?: boolean;
}

export function TopBar({
  userId = "default",
  editingUserId = false,
  showUserTooltip = false,
  userTooltipRef,
  onUserIdClick,
  onSaveUserId,
  onToggleTheme,
  onStartTour,
  searchTerm = "",
  onSearchChange,
  onReloadDb,
  reloadLoading = false,
}: TopBarProps) {
  const { theme, setTheme } = useTheme();
  const [lang, setLang] = useState("en");
  const [tempUserId, setTempUserId] = useState(userId);

  const handleThemeToggle = () => {
    if (onToggleTheme) {
      onToggleTheme();
    } else {
      setTheme(theme === "dark" ? "light" : "dark");
    }
  };

  const handleSaveUserId = () => {
    if (onSaveUserId) {
      onSaveUserId(tempUserId);
    }
  };

  return (
    <nav className="w-full flex items-center justify-between px-8 py-5 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-200/20 dark:border-gray-700/20">
      <div className="flex items-center gap-3">
        <span className="text-2xl">🗄️</span>
        <span className="font-extrabold text-xl text-blue-700 dark:text-blue-400">
          AI Database <span className="text-purple-600 dark:text-purple-400">Query System</span>
        </span>
      </div>

      <div className="flex items-center gap-4 flex-1 justify-end max-w-2xl">
        {/* Search Box */}
        {onSearchChange && (
          <div className="flex h-12 px-6 items-center flex-1 max-w-md rounded-full border-2 bg-white/50 dark:bg-[#232435]/20 border-gray-200 dark:border-white/12 text-gray-800 dark:text-white text-base min-w-0 gap-3">
            <SearchIcon />
            <input
              type="text"
              placeholder="Search"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="bg-transparent border-none outline-none text-gray-800 dark:text-white text-base flex-1 min-w-0"
            />
          </div>
        )}

        {/* Reload Button */}
        {onReloadDb && (
          <SecondaryButton
            text="Reload DB"
            iconPlacement="left"
            icon={
              (
                <ReloadIcon
                  fill={theme === "dark" ? "#fff" : "#222"}
                />
              ) as unknown as string
            }
            onClick={onReloadDb}
            width="140px"
            style={{
              minHeight: "40px",
            }}
            mode={theme === "dark" ? "dark" : "light"}
            disabled={reloadLoading}
          />
        )}

        <div className="flex items-center gap-3">
        {/* User ID Section */}
        <div className="relative">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-300">User:</span>
            <button
              onClick={onUserIdClick}
              className="px-3 py-1 text-sm font-semibold text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors"
            >
              {userId}
            </button>
          </div>

          {/* User ID Tooltip */}
          {showUserTooltip && (
            <div
              ref={userTooltipRef}
              className="absolute top-full right-0 mt-2 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 min-w-[200px]"
            >
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  User ID:
                </label>
                <input
                  type="text"
                  value={tempUserId}
                  onChange={(e) => setTempUserId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter user ID"
                />
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={handleSaveUserId}
                    className="flex-1"
                  >
                    Save
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={onUserIdClick}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Status Indicator */}
        <div className="flex items-center gap-2">
          <span className="h-3 w-3 rounded-full bg-green-500 inline-block animate-pulse"></span>
          <span className="text-sm font-semibold text-gray-600 dark:text-gray-300">Online</span>
        </div>

        {/* Theme Toggle */}
        <button
          className="w-12 h-12 rounded-full bg-white/50 dark:bg-[#232435]/50 flex items-center justify-center cursor-pointer transition-colors"
          onClick={handleThemeToggle}
          aria-label="Toggle dark mode"
        >
          <DarkModeToggleIcon width={28} height={28} />
        </button>

        {/* User Icon with Tooltip */}
        <div className="relative">
          <button
            className="w-12 h-12 rounded-full bg-white/50 dark:bg-[#232435]/50 flex items-center justify-center overflow-hidden cursor-pointer z-10 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              if (onUserIdClick) onUserIdClick();
            }}
            aria-label="User settings"
          >
            <UserIcon width={36} height={36} />
          </button>
        </div>
      </div>
    </nav>
  );
}