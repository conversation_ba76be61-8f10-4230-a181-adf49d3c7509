"use client";
import React, { useState } from "react";

interface UserTooltipProps {
  userId: string;
  editingUserId: boolean;
  showUserTooltip: boolean;
  userTooltipRef: React.RefObject<HTMLDivElement>;
  onSaveUserId: (userId: string) => void;
  onToggleEdit: () => void;
  onClose: () => void;
}

export const UserTooltip: React.FC<UserTooltipProps> = ({
  userId,
  editingUserId,
  showUserTooltip,
  userTooltipRef,
  onSaveUserId,
  onToggleEdit,
  onClose,
}) => {
  const [tempUserId, setTempUserId] = useState(userId === "default" ? "" : userId);

  const handleSave = () => {
    onSaveUserId(tempUserId || "default");
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSave();
    }
  };

  if (!showUserTooltip) return null;

  return (
    <div
      style={{
        zIndex: 1000,
      }}
      ref={userTooltipRef}
      className="absolute top-15 right-0 w-70 backdrop-blur-12 border-2 border-green-200/27 dark:border-green-200/27 bg-gradient-to-b from-green-200/25 via-green-200/9 to-green-200/2 dark:from-green-200/25 dark:via-green-200/9 dark:to-green-200/2 rounded-2xl p-4 shadow-2xl z-40"
    >
      <div className="mb-3">
        <h3 className="m-0 mb-2 text-sm font-semibold text-gray-800 dark:text-white">
          User ID Settings
        </h3>
        <p className="m-0 text-xs text-gray-600 dark:text-gray-300">
          {userId === "default"
            ? "Set your user ID to save query history"
            : `Current User ID: ${userId}`}
        </p>
      </div>

      {editingUserId ? (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "8px",
          }}
        >
          <input
            type="text"
            placeholder="Enter user ID"
            value={tempUserId}
            onChange={(e) => setTempUserId(e.target.value)}
            onKeyDown={handleKeyDown}
            autoFocus
            style={{
              background: "rgba(255, 255, 255, 0.1)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              borderRadius: "8px",
              padding: "8px 12px",
              color: "#FFFFFF",
              fontSize: "14px",
              outline: "none",
            }}
          />
          <div
            style={{
              display: "flex",
              gap: "8px",
              justifyContent: "flex-end",
            }}
          >
            <button
              onClick={onClose}
              style={{
                background: "rgba(255, 255, 255, 0.1)",
                border: "1px solid rgba(255, 255, 255, 0.2)",
                borderRadius: "6px",
                padding: "6px 12px",
                color: "#FFFFFF",
                fontSize: "12px",
                cursor: "pointer",
              }}
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              style={{
                background: "#5BE49B",
                border: "none",
                borderRadius: "6px",
                padding: "6px 12px",
                color: "#000000",
                fontSize: "12px",
                fontWeight: 500,
                cursor: "pointer",
              }}
            >
              Save
            </button>
          </div>
        </div>
      ) : (
        <button
          onClick={onToggleEdit}
          style={{
            background: "rgba(255, 255, 255, 0.1)",
            border: "1px solid rgba(255, 255, 255, 0.2)",
            borderRadius: "8px",
            padding: "8px 12px",
            color: "#FFFFFF",
            fontSize: "14px",
            cursor: "pointer",
            width: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: "8px",
          }}
        >
          <span>✏️</span>
          {userId === "default" ? "Set User ID" : "Change User ID"}
        </button>
      )}
    </div>
  );
};
