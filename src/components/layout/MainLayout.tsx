"use client";
import React from "react";
import { useTheme } from "next-themes";
import { Toast } from "@/components/Toast";
import { BackgroundBeamsWithCollision } from "@/components/ui/background-beams-with-collision";
import { EnhancedBackground } from "@/components/ui/enhanced-background";
import { OpeningAnimation } from "@/components/ui/opening-animation";

interface MainLayoutProps {
  children: React.ReactNode;
  showOpeningAnimation?: boolean;
  onOpeningComplete?: () => void;
  toast?: {
    isVisible: boolean;
    message: string;
    type: "success" | "error" | "info";
  };
  mounted?: boolean;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ 
  children,
  showOpeningAnimation = false,
  onOpeningComplete,
  toast = { isVisible: false, message: "", type: "success" },
  mounted = true
}) => {
  const { theme } = useTheme();

  return (
    <div className="min-h-screen">
      {/* Opening Animation */}
      {showOpeningAnimation && (
        <OpeningAnimation duration={4000} onComplete={onOpeningComplete}>
          <div />
        </OpeningAnimation>
      )}

      {/* Show loading state during hydration */}
      {!mounted && (
        <div className="w-full h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-300">Loading...</p>
          </div>
        </div>
      )}

      {/* Background elements */}
      {mounted && (
        <>
          {theme === "dark" ? (
            <BackgroundBeamsWithCollision intensity="medium">
              <div />
            </BackgroundBeamsWithCollision>
          ) : (
            <EnhancedBackground intensity="medium">
              <div />
            </EnhancedBackground>
          )}
        </>
      )}

      {/* Main content */}
      {mounted && children}

      {/* Toast notification */}
      {toast.isVisible && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => {}}
          isVisible={toast.isVisible}
        />
      )}
    </div>
  );
};