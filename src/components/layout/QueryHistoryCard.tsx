"use client";
import React from "react";
import { useTheme } from "next-themes";
import QueryHistoryIcon from "@/icons/sidebar/QueryHistoryIcon";

interface QueryHistoryItem {
  question: string;
  timestamp?: string;
}

interface QueryHistoryCardProps {
  history: QueryHistoryItem[];
  historyLoading: boolean;
  historyError: string | null;
  isTourMode: boolean;
  userId: string;
  selected: string;
  onClearHistory: () => void;
  onQuerySelect: (question: string) => void;
}

export const QueryHistoryCard: React.FC<QueryHistoryCardProps> = ({
  history,
  historyLoading,
  historyError,
  isTourMode,
  userId,
  selected,
  onClearHistory,
  onQuerySelect,
}) => {
  const { theme } = useTheme();

  // Don't show on dashboard or file system
  if (selected === "dashboard" || selected === "File system") {
    return null;
  }

  // Only show when in tour mode or user is set
  if (!isTourMode && !userId) {
    return null;
  }

  return (
    <div
      className="query-history"
      style={{
        display: "flex",
        padding: "20px 18px 20px 18px",
        flexDirection: "column",
        gap: "12px",
        alignSelf: "stretch",
        borderRadius: "24px",
        border:
          theme === "dark"
            ? "2px solid rgba(221,255,237,0.18)"
            : "1.5px solid #e5e7eb",
        background:
          theme === "dark"
            ? "linear-gradient(180deg, rgba(148,255,212,0.22) -4.62%, rgba(148,255,212,0.09) 52.78%, rgba(148,255,212,0.02) 103.39%)"
            : "#fff",
        boxShadow:
          theme === "dark"
            ? "0 4px 24px 0 rgba(0,0,0,0.07)"
            : "0 2px 12px 0 rgba(0,0,0,0.06)",
        backdropFilter: theme === "dark" ? "blur(32px)" : undefined,
        marginTop: "30px",
        width: "100%",
        color: theme === "dark" ? undefined : "#222",
      }}
    >
      {/* Header: icon, text, clear button */}
      <div className="flex items-center justify-between w-full mb-2">
        <div className="flex items-center gap-2">
          <QueryHistoryIcon className="w-5 h-5" fill="#fff" />
          <span
            className="text-base font-semibold dark:text-white text-black"
            style={{ letterSpacing: "-0.5px" }}
          >
            History
          </span>
        </div>
        <button
          onClick={onClearHistory}
          disabled={historyLoading}
          className="relative w-20 inline-flex h-9 overflow-hidden rounded-full p-[1px] focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50"
        >
          <span className="absolute inset-[-1000%] animate-[spin_2s_linear_infinite] bg-[conic-gradient(from_90deg_at_50%_50%,#E2CBFF_0%,#393BB2_50%,#E2CBFF_100%)]" />
          <span className="inline-flex h-full w-full cursor-pointer items-center justify-center rounded-full bg-slate-950 px-3 py-1 text-sm font-medium text-white backdrop-blur-3xl">
            Clear
          </span>
        </button>
      </div>

      {/* Query List */}
      <div className="flex flex-col gap-4 w-full">
        {history.map((item, i) => (
          <button
            key={i}
            className="w-full text-left truncate text-[15px] font-normal rounded-lg px-3 py-2 transition-all"
            title={item.question}
            onClick={() => onQuerySelect(item.question)}
            style={{
              fontWeight: 400,
              color: theme === "dark" ? "#E6F7EF" : "#222",
              fontSize: "15px",
              letterSpacing: "-0.2px",
              boxShadow: "none",
              background: "transparent",
            }}
          >
            {item.question.length > 32
              ? item.question.slice(0, 29) + "..."
              : item.question}
          </button>
        ))}
      </div>

      {/* Empty state */}
      {!historyLoading && !historyError && history.length === 0 && (
        <div
          className="text-xs mt-2 text-center py-2"
          style={{
            color: theme === "dark" ? "#B2D9C7" : "#888",
          }}
        >
          No history found
        </div>
      )}

      {/* Error state */}
      {historyError && (
        <div className="text-red-500 text-xs mt-2 text-center py-2">
          {historyError}
        </div>
      )}
    </div>
  );
};
