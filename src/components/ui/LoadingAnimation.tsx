"use client";
import React from "react";
import <PERSON><PERSON> from "lottie-react";

interface LoadingAnimationProps {
  isVisible: boolean;
  animationData: any;
  title?: string;
  subtitle?: string;
  size?: number;
}

export const LoadingAnimation: React.FC<LoadingAnimationProps> = ({
  isVisible,
  animationData,
  title = "Processing your request...",
  subtitle = "Our AI is analyzing your query and searching through the database",
  size = 120,
}) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 flex flex-col items-center gap-4">
        <Lottie
          animationData={animationData}
          style={{ width: size, height: size }}
          loop={true}
        />
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-2">
            {title}
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            {subtitle}
          </p>
        </div>
      </div>
    </div>
  );
};
