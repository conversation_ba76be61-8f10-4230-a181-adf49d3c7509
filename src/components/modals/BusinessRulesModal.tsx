"use client";
import React, { useRef } from "react";
import { useTheme } from "next-themes";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { PrimaryButton } from "@/components/glass-ui/buttons/PrimaryButton";
import { SecondaryButton } from "@/components/glass-ui/buttons/SecondaryButton";

interface BusinessRulesModalProps {
  isOpen: boolean;
  businessRulesText: string;
  businessRulesLoading: boolean;
  businessRulesError: string | null;
  isEditingRules: boolean;
  editRulesText: string;
  editPreview: boolean;
  uploadingRules: boolean;
  uploadError: string | null;
  uploadSuccess: boolean;
  downloadingRules: boolean;
  onClose: () => void;
  onEditRulesTextChange: (text: string) => void;
  onStartEdit: () => void;
  onCancelEdit: () => void;
  onSubmitEdit: () => void;
  onTogglePreview: () => void;
  onDownload: () => void;
}

export const BusinessRulesModal: React.FC<BusinessRulesModalProps> = ({
  isOpen,
  businessRulesText,
  businessRulesLoading,
  businessRulesError,
  isEditingRules,
  editRulesText,
  editPreview,
  uploadingRules,
  uploadError,
  uploadSuccess,
  downloadingRules,
  onClose,
  onEditRulesTextChange,
  onStartEdit,
  onCancelEdit,
  onSubmitEdit,
  onTogglePreview,
  onDownload,
}) => {
  const { theme } = useTheme();
  const editTextareaRef = useRef<HTMLTextAreaElement>(null);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 bg-black/40"
      style={{
        position: "fixed",
        inset: 0,
        zIndex: 50,
        background: "rgba(0,0,0,0.40)",
      }}
    >
      <div
        className="w-863 p-2 px-6 pb-6 flex flex-col justify-center items-center flex-shrink-0 rounded-3xl border-2"
        style={{
          position: "fixed",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          borderRadius: "32px",
          border:
            theme === "dark"
              ? "3px solid rgba(0, 191, 111, 0.27)"
              : "1.5px solid #e1f4ea",
          background:
            theme === "dark"
              ? "linear-gradient(180deg, rgba(0, 191, 111, 0.25) 0%, rgba(0, 191, 111, 0.09) 52.11%, rgba(0, 191, 111, 0.02) 100%)"
              : "#fff",
          boxShadow:
            theme === "dark" ? "none" : "0 2px 12px 0 rgba(0,0,0,0.06)",
          backdropFilter: theme === "dark" ? "blur(32px)" : undefined,
          maxWidth: "95vw",
          maxHeight: "95vh",
          overflow: "auto",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {/* Header */}
        <div className="flex items-center w-full p-0 mb-3">
          <h2 className="text-xl font-bold text-gray-800 dark:text-gray-100 flex-1">
            Business Rules
          </h2>
          <button
            className="text-gray-500 hover:text-gray-800 dark:hover:text-gray-200 text-2xl px-2 py-1 rounded-full focus:outline-none"
            onClick={onClose}
            aria-label="Close"
          >
            ×
          </button>
        </div>

        {/* Inner Card for Content */}
        <div
          className="flex p-6 items-start gap-6 self-stretch rounded-3xl w-full mt-6 min-h-300"
          style={{
            background:
              theme === "dark" ? "rgba(0, 191, 111, 0.10)" : "#f0f9f5",
            borderRadius: "24px",
          }}
        >
          {/* Content */}
          <div className="w-full">
            {businessRulesLoading ? (
              <div className="flex items-center justify-center h-full">
                <svg
                  className="animate-spin h-8 w-8 text-[#00bf6f]"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              </div>
            ) : businessRulesError ? (
              <div className="text-red-500 text-center font-medium">
                {businessRulesError}
              </div>
            ) : isEditingRules ? (
              editPreview ? (
                <div className="flex flex-col h-full w-full">
                  <div className="flex-1 markdown-body max-h-[55vh] overflow-auto bg-gray-50 dark:bg-[#012920]/40 rounded-xl p-4 border border-gray-100 dark:border-[#013828]/40">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {editRulesText}
                    </ReactMarkdown>
                  </div>
                  <div className="flex gap-2 mt-4 justify-end max-w-40">
                    <PrimaryButton
                      onClick={() => onTogglePreview()}
                      disabled={uploadingRules}
                      text="Back to Edit"
                    />
                  </div>
                </div>
              ) : (
                <div className="flex flex-col h-full w-full">
                  <div className="flex-1 min-h-0 flex flex-col">
                    <textarea
                      ref={editTextareaRef}
                      className="w-full flex-1 rounded-xl border border-gray-200 dark:border-[#013828]/40 p-4 font-mono text-sm text-gray-800 dark:text-gray-100 bg-gray-50 dark:bg-[#012920]/40 resize-none focus:outline-none"
                      style={{
                        minHeight: "300px",
                        maxHeight: "55vh",
                        height: "100%",
                      }}
                      value={editRulesText}
                      onChange={(e) => onEditRulesTextChange(e.target.value)}
                      disabled={uploadingRules}
                    />
                  </div>
                  <div className="flex gap-2 mt-4">
                    <div className="">
                      <PrimaryButton
                        text="Submit"
                        onClick={onSubmitEdit}
                        disabled={uploadingRules}
                        className="h-[40px] w-32"
                      />
                    </div>
                    <SecondaryButton
                      text="Cancel"
                      style={{ width: "70px" }}
                      onClick={onCancelEdit}
                      disabled={uploadingRules}
                    />
                    <SecondaryButton
                      text="Preview"
                      style={{ width: "70px" }}
                      onClick={() => onTogglePreview()}
                      disabled={uploadingRules}
                    />
                  </div>
                  {uploadError && (
                    <div className="text-red-500 mt-2">{uploadError}</div>
                  )}
                  {uploadSuccess && (
                    <div className="text-green-600 mt-2">
                      Business rules updated!
                    </div>
                  )}
                </div>
              )
            ) : (
              <div className="markdown-body max-h-[55vh] overflow-auto text-gray-800 dark:text-white text-base leading-7">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {businessRulesText}
                </ReactMarkdown>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        {!isEditingRules && (
          <div className="flex items-center justify-between w-full mt-6">
            <SecondaryButton
              style={{}}
              className="h-8"
              text="Cancel"
              onClick={onClose}
              mode={theme === "dark" ? "dark" : "light"}
            />
            <div className="flex items-center gap-2">
              {!businessRulesLoading && !isEditingRules && (
                <SecondaryButton
                  text="Edit"
                  onClick={onStartEdit}
                  style={{ width: "70px" }}
                  mode={theme === "dark" ? "dark" : "light"}
                />
              )}
              <PrimaryButton
                text="Download"
                onClick={onDownload}
                disabled={businessRulesLoading || downloadingRules}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
